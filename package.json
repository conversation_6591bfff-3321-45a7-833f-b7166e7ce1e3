{"name": "smartfinance", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.9.0", "expo": "~53.0.10", "expo-av": "^15.1.5", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}