import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Animated } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Audio } from 'expo-av';
import { COLORS } from '../constants';

interface VoiceButtonProps {
  onVoiceResult: (text: string) => void;
  disabled?: boolean;
}

const VoiceButton: React.FC<VoiceButtonProps> = ({ onVoiceResult, disabled = false }) => {
  const [isListening, setIsListening] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [countdown, setCountdown] = useState(3);
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    return () => {
      cleanupRecording();
    };
  }, []);

  const cleanupRecording = async () => {
    try {
      if (recording) {
        await recording.stopAndUnloadAsync();
        setRecording(null);
      }
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: false,
      });
    } catch (error) {
      console.error('Error in cleanup:', error);
    }
    setIsListening(false);
    scaleAnim.setValue(1);
  };

  const startListening = async () => {
    try {
      if (isListening || recording) {
        return;
      }

      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Izin Diperlukan', 'Mohon berikan izin mikrofon untuk menggunakan input suara.');
        return;
      }

      setIsListening(true);

      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(newRecording);

      setCountdown(3);
      let currentCountdown = 3;

      const countdownInterval = setInterval(() => {
        currentCountdown -= 1;
        setCountdown(currentCountdown);

        if (currentCountdown <= 0) {
          clearInterval(countdownInterval);
          stopListening();
        }
      }, 1000);

    } catch (error) {
      console.error('Error starting voice recording:', error);
      Alert.alert('Kesalahan', 'Gagal memulai perekaman suara. Silakan coba lagi.');
      await cleanupRecording();
    }
  };

  const stopListening = async () => {
    try {
      setIsListening(false);
      scaleAnim.stopAnimation();
      scaleAnim.setValue(1);

      if (recording) {
        try {
          await recording.stopAndUnloadAsync();
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            playsInSilentModeIOS: false,
          });
          setRecording(null);
          simulateVoiceToText();
        } catch (stopError) {
          console.error('Error stopping recording:', stopError);
          setRecording(null);
        }
      }
    } catch (error) {
      console.error('Error in stopListening:', error);
      Alert.alert('Kesalahan', 'Gagal memproses perekaman suara.');
      setRecording(null);
    }
  };

  const simulateVoiceToText = () => {
    const sampleTexts = [
      'Saya beli kopi 25 ribu',
      'Bayar bensin 50000',
      'Terima gaji 5 juta',
      'Beli makan siang 30 ribu',
      'Dapat bonus 1 juta',
    ];

    const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];

    setTimeout(() => {
      onVoiceResult(randomText);
    }, 1000);
  };

  const handlePress = () => {
    if (disabled) return;

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          isListening && styles.buttonListening,
          disabled && styles.buttonDisabled,
        ]}
        onPress={handlePress}
        disabled={disabled}
      >
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Icon
            name={isListening ? 'mic' : 'mic-none'}
            size={32}
            color={COLORS.surface}
          />
        </Animated.View>
      </TouchableOpacity>

      <Text style={styles.instruction}>
        {isListening
          ? `Mendengarkan... ${countdown}s`
          : 'Tekan tombol mikrofon dan katakan sesuatu seperti:'
        }
      </Text>

      {!isListening && (
        <View style={styles.examples}>
          <Text style={styles.exampleText}>"Beli makan siang 25 ribu"</Text>
          <Text style={styles.exampleText}>"Terima gaji 5 juta"</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  button: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonListening: {
    backgroundColor: COLORS.error,
  },
  buttonDisabled: {
    backgroundColor: COLORS.textSecondary,
  },
  instruction: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  examples: {
    alignItems: 'center',
  },
  exampleText: {
    fontSize: 14,
    color: COLORS.primary,
    fontStyle: 'italic',
    marginVertical: 2,
  },
});

export default VoiceButton;