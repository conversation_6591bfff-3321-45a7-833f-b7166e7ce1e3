import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Animated, Platform } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Audio } from 'expo-av';
import { COLORS } from '../constants';

interface VoiceButtonProps {
  onVoiceResult: (text: string) => void;
  disabled?: boolean;
}

const NUM_BARS = 5; // Number of spectrum bars

const VoiceButton: React.FC<VoiceButtonProps> = ({ onVoiceResult, disabled = false }) => {
  const [isListening, setIsListening] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recognition, setRecognition] = useState<any>(null);
  const scaleAnim = new Animated.Value(1);
  
  // Create animated values for spectrum bars
  const spectrumAnimations = Array.from({ length: NUM_BARS }, () => new Animated.Value(0));

  // Animation for spectrum visualization
  const animateSpectrum = () => {
    if (!isListening) return;

    const animations = spectrumAnimations.map((anim) => {
      const randomHeight = Math.random() * 0.8 + 0.2; // Random height between 0.2 and 1
      return Animated.sequence([
        Animated.timing(anim, {
          toValue: randomHeight,
          duration: 200 + Math.random() * 300, // Random duration
          useNativeDriver: true,
        }),
        Animated.timing(anim, {
          toValue: 0.2,
          duration: 200 + Math.random() * 300,
          useNativeDriver: true,
        }),
      ]);
    });

    Animated.parallel(animations).start(() => {
      if (isListening) {
        animateSpectrum(); // Continue animation if still listening
      }
    });
  };

  useEffect(() => {
    // Initialize speech recognition for web
    if (Platform.OS === 'web' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'id-ID'; // Indonesian language

      recognitionInstance.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        handleVoiceResult(transcript);
      };

      recognitionInstance.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        Alert.alert('Kesalahan', 'Gagal mengenali suara. Silakan coba lagi.');
        setIsListening(false);
        scaleAnim.setValue(1);
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
        scaleAnim.setValue(1);
      };

      setRecognition(recognitionInstance);
    }

    return () => {
      cleanupRecording();
    };
  }, []);

  const handleVoiceResult = async (text: string) => {
    try {
      onVoiceResult(text);
    } catch (error) {
      console.error('Error processing voice input:', error);
      Alert.alert('Kesalahan', 'Gagal memproses input suara. Silakan coba lagi.');
    } finally {
      setIsListening(false);
      scaleAnim.setValue(1);
    }
  };

  const cleanupRecording = async () => {
    try {
      // Stop web speech recognition
      if (Platform.OS === 'web' && recognition) {
        recognition.stop();
      }

      // Stop mobile recording
      if (recording) {
        await recording.stopAndUnloadAsync();
        setRecording(null);
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: false,
      });
    } catch (error) {
      console.error('Error in cleanup:', error);
    }
    setIsListening(false);
    scaleAnim.setValue(1);
  };

  const startListening = async () => {
    try {
      if (isListening) return;

      setIsListening(true);
      animateSpectrum(); // Start spectrum animation

      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Use Web Speech API for web platform
      if (Platform.OS === 'web' && recognition) {
        recognition.start();
        
        // Auto-stop after 5 seconds for web
        setTimeout(() => {
          if (isListening && recognition) {
            recognition.stop();
          }
        }, 5000);
      } else {
        // For mobile platforms, use audio recording + simulation
        const { status } = await Audio.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Izin Diperlukan', 'Mohon berikan izin mikrofon untuk menggunakan input suara.');
          setIsListening(false);
          scaleAnim.setValue(1);
          return;
        }

        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });

        const { recording: newRecording } = await Audio.Recording.createAsync(
          Audio.RecordingOptionsPresets.HIGH_QUALITY
        );
        setRecording(newRecording);

        // Auto-stop after 5 seconds
        setTimeout(() => {
          if (isListening) {
            stopListening();
          }
        }, 5000);
      }
    } catch (error) {
      console.error('Error starting voice recording:', error);
      Alert.alert('Kesalahan', 'Gagal memulai perekaman suara. Silakan coba lagi.');
      await cleanupRecording();
    }
  };

  const stopListening = async () => {
    try {
      setIsListening(false);
      scaleAnim.stopAnimation();
      scaleAnim.setValue(1);

      // Stop web speech recognition
      if (Platform.OS === 'web' && recognition) {
        recognition.stop();
      }

      // Stop mobile recording and process with Speech API
      if (recording) {
        try {
          await recording.stopAndUnloadAsync();
          const uri = recording.getURI();
          
          // For demo purposes, we'll use sample texts since mobile speech-to-text
          // requires additional setup
          const sampleTexts = [
            'Beli kopi 25 ribu',
            'Bayar bensin 50 ribu',
            'Terima gaji 5 juta',
            'Beli makan siang 30 ribu',
            'Dapat bonus 1 juta',
            'Bayar listrik 200 ribu',
            'Beli groceries 150 ribu',
            'Terima transfer 2 juta',
          ];
          const demoText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
          
          // In production, you would:
          // 1. Convert the audio file to base64
          // 2. Send to a Speech-to-Text service
          // 3. Get the transcription
          // For now, we'll use the demo text
          handleVoiceResult(demoText);

          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            playsInSilentModeIOS: false,
          });
          setRecording(null);
        } catch (stopError) {
          console.error('Error stopping recording:', stopError);
          setRecording(null);
        }
      }
    } catch (error) {
      console.error('Error in stopListening:', error);
      Alert.alert('Kesalahan', 'Gagal memproses perekaman suara.');
      setRecording(null);
    }
  };

  const handlePress = () => {
    if (disabled) return;

    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.spectrumContainer}>
        {spectrumAnimations.map((anim, index) => (
          <Animated.View
            key={index}
            style={[
              styles.spectrumBar,
              {
                transform: [
                  { scaleY: anim },
                  { translateY: 20 }, // Offset for bottom alignment
                ],
              },
            ]}
          />
        ))}
      </View>

      <TouchableOpacity
        style={[
          styles.button,
          isListening && styles.buttonListening,
          disabled && styles.buttonDisabled,
        ]}
        onPress={handlePress}
        disabled={disabled}
      >
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Icon
            name={isListening ? 'mic' : 'mic-none'}
            size={32}
            color={COLORS.surface}
          />
        </Animated.View>
      </TouchableOpacity>

      <Text style={styles.instruction}>
        {isListening
          ? 'Mendengarkan... Bicara sekarang!'
          : 'Tekan tombol mikrofon dan katakan sesuatu seperti:'
        }
      </Text>

      {!isListening && (
        <View style={styles.examples}>
          <Text style={styles.exampleText}>"Beli makan siang 25 ribu"</Text>
          <Text style={styles.exampleText}>"Terima gaji 5 juta"</Text>
          {Platform.OS === 'web' ? (
            <Text style={styles.infoText}>
              💡 Voice recognition aktif di browser
            </Text>
          ) : (
            <Text style={styles.infoText}>
              💡 Demo mode - akan generate contoh transaksi
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  button: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  buttonListening: {
    backgroundColor: COLORS.error,
  },
  buttonDisabled: {
    backgroundColor: COLORS.textSecondary,
  },
  instruction: {
    fontSize: 16,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  examples: {
    alignItems: 'center',
  },
  exampleText: {
    fontSize: 14,
    color: COLORS.primary,
    fontStyle: 'italic',
    marginVertical: 2,
  },
  infoText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    fontStyle: 'italic',
  },
  spectrumContainer: {
    flexDirection: 'row',
    height: 40,
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginBottom: 16,
    opacity: 0.8,
  },
  spectrumBar: {
    width: 4,
    height: 40,
    backgroundColor: COLORS.primary,
    marginHorizontal: 2,
    borderRadius: 2,
  },
});

export default VoiceButton;