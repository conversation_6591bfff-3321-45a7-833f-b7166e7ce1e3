import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Animated } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as Speech from 'expo-speech';
import { Audio } from 'expo-av';
import { COLORS } from '../constants';

interface VoiceButtonProps {
  onVoiceResult: (text: string) => void;
  disabled?: boolean;
}

const VoiceButton: React.FC<VoiceButtonProps> = ({ onVoiceResult, disabled = false }) => {
  const [isListening, setIsListening] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [silenceTimer, setSilenceTimer] = useState<NodeJS.Timeout | null>(null);
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      stopRecording();
    };
  }, []);

  const stopRecording = async () => {
    if (recording) {
      try {
        await recording.stopAndUnloadAsync();
      } catch (error) {
        console.error('Error stopping recording:', error);
      }
      setRecording(null);
    }
    setIsListening(false);
    scaleAnim.setValue(1);
    if (silenceTimer) {
      clearTimeout(silenceTimer);
      setSilenceTimer(null);
    }
  };

  const startListening = async () => {
    try {
      // First ensure any existing recording is stopped and unloaded
      await stopRecording();

      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Izin Diperlukan', 'Mohon berikan izin mikrofon untuk menggunakan input suara.');
        return;
      }

      setIsListening(true);
      
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create and prepare recording
      const newRecording = new Audio.Recording();
      await newRecording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
      await newRecording.startAsync();
      setRecording(newRecording);

      // Set up silence detection timer
      const timer = setTimeout(() => {
        if (isListening) {
          stopListening();
        }
      }, 5000); // 5 seconds of silence
      setSilenceTimer(timer);

    } catch (error) {
      console.error('Error starting voice recording:', error);
      Alert.alert('Kesalahan', 'Gagal memulai perekaman suara. Silakan coba lagi.');
      setIsListening(false);
    }
  };

  const stopListening = async () => {
    try {
      setIsListening(false);
      scaleAnim.stopAnimation();
      scaleAnim.setValue(1);

      if (silenceTimer) {
        clearTimeout(silenceTimer);
        setSilenceTimer(null);
      }

      if (recording) {
        try {
          await recording.stopAndUnloadAsync();
          // Important: unload and cleanup recording
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            playsInSilentModeIOS: false,
          });
          setRecording(null);

          // Simulate voice recognition result
          simulateVoiceToText();
        } catch (stopError) {
          console.error('Error stopping recording:', stopError);
          setRecording(null);
        }
      }
    } catch (error) {
      console.error('Error in stopListening:', error);
      Alert.alert('Kesalahan', 'Gagal memproses perekaman suara.');
      setRecording(null);
    }
  };

  const simulateVoiceToText = () => {
    // This is a simulation of voice-to-text. In a real app, you would:
    // 1. Send the audio file to a speech-to-text service
    // 2. Get the transcribed text back
    // 3. Call onVoiceResult with the text
    const sampleTexts = [
      'Saya beli kopi 25 ribu',
      'Bayar bensin 50000',
      'Terima gaji 5 juta',
      'Belanja bulanan 750 ribu',
    ];
    
    const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
    onVoiceResult(randomText);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={isListening ? stopListening : startListening}
        disabled={disabled}
        style={[styles.button, disabled && styles.buttonDisabled]}
      >
        <Animated.View style={[styles.buttonInner, { transform: [{ scale: scaleAnim }] }]}>
          <Icon 
            name={isListening ? "stop" : "mic"} 
            size={32} 
            color={disabled ? COLORS.textSecondary : COLORS.surface} 
          />
        </Animated.View>
      </TouchableOpacity>
      {isListening && (
        <Text style={styles.listeningText}>Mendengarkan...</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  buttonDisabled: {
    backgroundColor: COLORS.cardBorder,
  },
  buttonInner: {
    width: '100%',
    height: '100%',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listeningText: {
    marginTop: 8,
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default VoiceButton;
